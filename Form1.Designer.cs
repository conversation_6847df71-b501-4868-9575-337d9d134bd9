﻿namespace 一键提权
{
    partial class Form1
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        private AntdUI.PageHeader pageHeader;
        private AntdUI.Panel panelMain;
        private AntdUI.Panel panelFile;
        private AntdUI.Label lblFilePath;
        private AntdUI.Input txtFilePath;
        private AntdUI.Button btnSelectFile;
        private AntdUI.Checkbox chkCustomRunPath;
        private AntdUI.Label lblRunPath;
        private AntdUI.Input txtRunPath;
        private AntdUI.Button btnBrowseRunPath;
        private AntdUI.Panel panelActions;
        private AntdUI.Button btnRunAsAdmin;
        private AntdUI.Panel panelStatus;
        private AntdUI.Label lblStatus;
        private AntdUI.Input txtStatus;

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.pageHeader = new AntdUI.PageHeader();
            this.panelMain = new AntdUI.Panel();
            this.panelFile = new AntdUI.Panel();
            this.lblFilePath = new AntdUI.Label();
            this.txtFilePath = new AntdUI.Input();
            this.btnSelectFile = new AntdUI.Button();
            this.chkCustomRunPath = new AntdUI.Checkbox();
            this.lblRunPath = new AntdUI.Label();
            this.txtRunPath = new AntdUI.Input();
            this.btnBrowseRunPath = new AntdUI.Button();
            this.panelActions = new AntdUI.Panel();
            this.btnRunAsAdmin = new AntdUI.Button();
            this.panelStatus = new AntdUI.Panel();
            this.lblStatus = new AntdUI.Label();
            this.txtStatus = new AntdUI.Input();
            this.panelMain.SuspendLayout();
            this.panelFile.SuspendLayout();
            this.panelActions.SuspendLayout();
            this.panelStatus.SuspendLayout();
            this.SuspendLayout();
            //
            // pageHeader
            //
            this.pageHeader.Dock = System.Windows.Forms.DockStyle.Top;
            this.pageHeader.Location = new System.Drawing.Point(0, 0);
            this.pageHeader.Name = "pageHeader";
            this.pageHeader.Size = new System.Drawing.Size(600, 60);
            this.pageHeader.TabIndex = 0;
            this.pageHeader.Text = "一键提权工具";
            this.pageHeader.SubText = "使用lsrunase.exe以管理员权限运行程序";
            this.pageHeader.ShowIcon = true;
            this.pageHeader.IconSvg = "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z";
            //
            // panelMain
            //
            this.panelMain.Controls.Add(this.panelFile);
            this.panelMain.Controls.Add(this.panelActions);
            this.panelMain.Controls.Add(this.panelStatus);
            this.panelMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelMain.Location = new System.Drawing.Point(0, 60);
            this.panelMain.Name = "panelMain";
            this.panelMain.Padding = new System.Windows.Forms.Padding(20);
            this.panelMain.Size = new System.Drawing.Size(600, 440);
            this.panelMain.TabIndex = 1;
            //
            // panelFile
            //
            this.panelFile.Controls.Add(this.lblFilePath);
            this.panelFile.Controls.Add(this.txtFilePath);
            this.panelFile.Controls.Add(this.btnSelectFile);
            this.panelFile.Controls.Add(this.chkCustomRunPath);
            this.panelFile.Controls.Add(this.lblRunPath);
            this.panelFile.Controls.Add(this.txtRunPath);
            this.panelFile.Controls.Add(this.btnBrowseRunPath);
            this.panelFile.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelFile.Location = new System.Drawing.Point(20, 20);
            this.panelFile.Name = "panelFile";
            this.panelFile.Size = new System.Drawing.Size(560, 160);
            this.panelFile.TabIndex = 0;
            this.panelFile.BackColor = System.Drawing.Color.White;
            this.panelFile.BorderWidth = 1;
            this.panelFile.BorderColor = System.Drawing.Color.LightGray;
            this.panelFile.Radius = 8;
            //
            // lblFilePath
            //
            this.lblFilePath.AutoSize = true;
            this.lblFilePath.Location = new System.Drawing.Point(20, 20);
            this.lblFilePath.Name = "lblFilePath";
            this.lblFilePath.Size = new System.Drawing.Size(89, 12);
            this.lblFilePath.TabIndex = 0;
            this.lblFilePath.Text = "选择exe文件";
            //
            // txtFilePath
            //
            this.txtFilePath.Location = new System.Drawing.Point(20, 45);
            this.txtFilePath.Name = "txtFilePath";
            this.txtFilePath.PlaceholderText = "请选择需要提权运行的exe文件...";
            this.txtFilePath.ReadOnly = true;
            this.txtFilePath.Size = new System.Drawing.Size(400, 32);
            this.txtFilePath.TabIndex = 1;
            //
            // btnSelectFile
            //
            this.btnSelectFile.Location = new System.Drawing.Point(440, 45);
            this.btnSelectFile.Name = "btnSelectFile";
            this.btnSelectFile.Size = new System.Drawing.Size(100, 32);
            this.btnSelectFile.TabIndex = 2;
            this.btnSelectFile.Text = "选择文件";
            this.btnSelectFile.Type = AntdUI.TTypeMini.Primary;
            this.btnSelectFile.Click += new System.EventHandler(this.btnSelectFile_Click);
            //
            // chkCustomRunPath
            //
            this.chkCustomRunPath.AutoSize = true;
            this.chkCustomRunPath.Location = new System.Drawing.Point(20, 90);
            this.chkCustomRunPath.Name = "chkCustomRunPath";
            this.chkCustomRunPath.Size = new System.Drawing.Size(108, 16);
            this.chkCustomRunPath.TabIndex = 3;
            this.chkCustomRunPath.Text = "自定义运行路径";
            this.chkCustomRunPath.CheckedChanged += new AntdUI.BoolEventHandler(this.chkCustomRunPath_CheckedChanged);
            //
            // lblRunPath
            //
            this.lblRunPath.AutoSize = true;
            this.lblRunPath.Location = new System.Drawing.Point(20, 115);
            this.lblRunPath.Name = "lblRunPath";
            this.lblRunPath.Size = new System.Drawing.Size(65, 12);
            this.lblRunPath.TabIndex = 4;
            this.lblRunPath.Text = "运行路径";
            //
            // txtRunPath
            //
            this.txtRunPath.Enabled = false;
            this.txtRunPath.Location = new System.Drawing.Point(100, 110);
            this.txtRunPath.Name = "txtRunPath";
            this.txtRunPath.PlaceholderText = "程序运行的工作目录...";
            this.txtRunPath.Size = new System.Drawing.Size(320, 32);
            this.txtRunPath.TabIndex = 5;
            //
            // btnBrowseRunPath
            //
            this.btnBrowseRunPath.Enabled = false;
            this.btnBrowseRunPath.Location = new System.Drawing.Point(440, 110);
            this.btnBrowseRunPath.Name = "btnBrowseRunPath";
            this.btnBrowseRunPath.Size = new System.Drawing.Size(100, 32);
            this.btnBrowseRunPath.TabIndex = 6;
            this.btnBrowseRunPath.Text = "浏览";
            this.btnBrowseRunPath.Click += new System.EventHandler(this.btnBrowseRunPath_Click);
            //
            // panelActions
            //
            this.panelActions.Controls.Add(this.btnRunAsAdmin);
            this.panelActions.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelActions.Location = new System.Drawing.Point(20, 180);
            this.panelActions.Name = "panelActions";
            this.panelActions.Size = new System.Drawing.Size(560, 80);
            this.panelActions.TabIndex = 1;
            this.panelActions.BackColor = System.Drawing.Color.White;
            this.panelActions.BorderWidth = 1;
            this.panelActions.BorderColor = System.Drawing.Color.LightGray;
            this.panelActions.Radius = 8;
            //
            // btnRunAsAdmin
            //
            this.btnRunAsAdmin.Enabled = false;
            this.btnRunAsAdmin.Location = new System.Drawing.Point(20, 20);
            this.btnRunAsAdmin.Name = "btnRunAsAdmin";
            this.btnRunAsAdmin.Size = new System.Drawing.Size(150, 40);
            this.btnRunAsAdmin.TabIndex = 0;
            this.btnRunAsAdmin.Text = "🚀 提权运行";
            this.btnRunAsAdmin.Type = AntdUI.TTypeMini.Primary;
            this.btnRunAsAdmin.Click += new System.EventHandler(this.btnRunAsAdmin_Click);
            //
            // panelStatus
            //
            this.panelStatus.Controls.Add(this.lblStatus);
            this.panelStatus.Controls.Add(this.txtStatus);
            this.panelStatus.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelStatus.Location = new System.Drawing.Point(20, 260);
            this.panelStatus.Name = "panelStatus";
            this.panelStatus.Size = new System.Drawing.Size(560, 160);
            this.panelStatus.TabIndex = 2;
            this.panelStatus.BackColor = System.Drawing.Color.White;
            this.panelStatus.BorderWidth = 1;
            this.panelStatus.BorderColor = System.Drawing.Color.LightGray;
            this.panelStatus.Radius = 8;
            //
            // lblStatus
            //
            this.lblStatus.AutoSize = true;
            this.lblStatus.Location = new System.Drawing.Point(20, 20);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(53, 12);
            this.lblStatus.TabIndex = 0;
            this.lblStatus.Text = "状态信息";
            //
            // txtStatus
            //
            this.txtStatus.Location = new System.Drawing.Point(20, 45);
            this.txtStatus.Multiline = true;
            this.txtStatus.Name = "txtStatus";
            this.txtStatus.ReadOnly = true;
            this.txtStatus.Size = new System.Drawing.Size(520, 95);
            this.txtStatus.TabIndex = 1;
            this.txtStatus.PlaceholderText = "程序运行状态将在这里显示...";
            //
            // Form1
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(240, 242, 245);
            this.ClientSize = new System.Drawing.Size(600, 500);
            this.Controls.Add(this.panelMain);
            this.Controls.Add(this.pageHeader);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Name = "Form1";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "一键提权工具";
            this.panelMain.ResumeLayout(false);
            this.panelFile.ResumeLayout(false);
            this.panelFile.PerformLayout();
            this.panelActions.ResumeLayout(false);
            this.panelStatus.ResumeLayout(false);
            this.panelStatus.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
    }
}

