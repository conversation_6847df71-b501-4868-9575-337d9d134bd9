using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace 一键提权
{
    public partial class Form1 : Form
    {
        private string selectedFilePath = "";
        private const string ADMIN_USER = "administrator";
        private const string ENCRYPTED_PASSWORD = "40lkg/kN/6Pm3ffhFw==";
        private const string LSRUNASE_EXE = "lsrunase.exe";

        public Form1()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // 初始化状态信息
            AppendStatus("一键提权工具已启动");
            AppendStatus($"lsrunase.exe路径: {GetLsrunasePath()}");

            // 检查lsrunase.exe是否存在
            if (!File.Exists(GetLsrunasePath()))
            {
                AppendStatus("警告: 未找到lsrunase.exe文件！");
                btnRunAsAdmin.Enabled = false;
            }
        }

        private string GetLsrunasePath()
        {
            string appDir = Path.GetDirectoryName(Application.ExecutablePath);
            string lsrunasePath = Path.Combine(appDir, "提权程序", LSRUNASE_EXE);

            // 如果在提权程序目录找不到，尝试在当前目录查找
            if (!File.Exists(lsrunasePath))
            {
                lsrunasePath = Path.Combine(appDir, LSRUNASE_EXE);
            }

            return lsrunasePath;
        }

        private void AppendStatus(string message)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            string newText = $"[{timestamp}] {message}\r\n";

            // AntdUI的Input组件使用Text属性
            if (string.IsNullOrEmpty(txtStatus.Text))
            {
                txtStatus.Text = newText;
            }
            else
            {
                txtStatus.Text += newText;
            }

            Application.DoEvents();
        }

        private void btnSelectFile_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "可执行文件 (*.exe)|*.exe|所有文件 (*.*)|*.*";
                openFileDialog.Title = "选择要提权运行的exe文件";
                openFileDialog.RestoreDirectory = true;

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    selectedFilePath = openFileDialog.FileName;
                    txtFilePath.Text = selectedFilePath;

                    // 自动设置运行路径为exe文件所在目录
                    if (!chkCustomRunPath.Checked)
                    {
                        txtRunPath.Text = Path.GetDirectoryName(selectedFilePath);
                    }

                    // 启用操作按钮
                    if (File.Exists(GetLsrunasePath()))
                    {
                        btnRunAsAdmin.Enabled = true;
                    }

                    AppendStatus($"已选择文件: {Path.GetFileName(selectedFilePath)}");
                    AppendStatus($"文件路径: {selectedFilePath}");
                }
            }
        }

        private void btnRunAsAdmin_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(selectedFilePath) || !File.Exists(selectedFilePath))
            {
                MessageBox.Show("请先选择有效的exe文件！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            string lsrunasePath = GetLsrunasePath();
            if (!File.Exists(lsrunasePath))
            {
                MessageBox.Show("未找到lsrunase.exe文件！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                string runPath = chkCustomRunPath.Checked ? txtRunPath.Text : Path.GetDirectoryName(selectedFilePath);
                if (string.IsNullOrEmpty(runPath))
                {
                    runPath = Path.GetDirectoryName(selectedFilePath);
                }

                // 构建lsrunase命令参数
                string arguments = $"/user:{ADMIN_USER} /password:{ENCRYPTED_PASSWORD} /domain: /command:\"{selectedFilePath}\" /runpath:\"{runPath}\"";

                AppendStatus("正在启动提权程序...");
                AppendStatus($"命令: {LSRUNASE_EXE} {arguments}");

                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = lsrunasePath,
                    Arguments = arguments,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using (Process process = Process.Start(startInfo))
                {
                    process.WaitForExit(5000); // 等待5秒

                    if (process.HasExited)
                    {
                        if (process.ExitCode == 0)
                        {
                            AppendStatus("程序启动成功！");
                        }
                        else
                        {
                            AppendStatus($"程序启动失败，退出代码: {process.ExitCode}");
                            string error = process.StandardError.ReadToEnd();
                            if (!string.IsNullOrEmpty(error))
                            {
                                AppendStatus($"错误信息: {error}");
                            }
                        }
                    }
                    else
                    {
                        AppendStatus("程序已在后台启动");
                    }
                }
            }
            catch (Exception ex)
            {
                AppendStatus($"启动失败: {ex.Message}");
                MessageBox.Show($"启动失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }





        private void chkCustomRunPath_CheckedChanged(object sender, EventArgs e)
        {
            bool isChecked = chkCustomRunPath.Checked;
            txtRunPath.Enabled = isChecked;
            btnBrowseRunPath.Enabled = isChecked;

            if (!isChecked && !string.IsNullOrEmpty(selectedFilePath))
            {
                txtRunPath.Text = Path.GetDirectoryName(selectedFilePath);
            }

            AppendStatus($"自定义运行路径: {(isChecked ? "启用" : "禁用")}");
        }

        private void btnBrowseRunPath_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog folderDialog = new FolderBrowserDialog())
            {
                folderDialog.Description = "选择程序运行路径";
                folderDialog.ShowNewFolderButton = true;

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    txtRunPath.Text = folderDialog.SelectedPath;
                    AppendStatus($"运行路径设置为: {folderDialog.SelectedPath}");
                }
            }
        }
    }
}
