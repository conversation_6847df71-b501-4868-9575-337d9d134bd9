# 一键提权工具

这是一个基于C# Windows Forms开发的一键提权工具，可以帮助用户以管理员权限运行指定的exe程序。

## 功能特性

### 1. 文件选择功能
- 提供友好的文件选择对话框
- 支持选择任意exe文件
- 自动显示选择的文件路径

### 2. 提权执行功能
- 使用lsrunase.exe工具实现提权
- 预配置管理员账户和加密密码
- 支持自定义程序运行路径
- 实时显示执行状态和结果

### 3. 现代化界面
- 使用AntdUI组件库，界面美观现代
- 响应式布局，用户体验优秀
- 实时状态显示和操作日志记录
- 支持占位符提示和图标显示

## 使用方法

1. 启动程序
2. 点击"选择文件"按钮，选择需要提权运行的exe文件
3. 可选：勾选"自定义运行路径"并设置程序运行目录
4. 点击"提权运行"按钮执行程序

## 技术说明

### 核心组件
- **lsrunase.exe**: 提权执行工具，runas的增强版本
- **AntdUI**: 现代化UI组件库，提供美观的界面组件
- **Form1.cs**: 主界面和业务逻辑

### 提权参数
- 用户名: administrator
- 加密密码: 40lkg/kN/6Pm3ffhFw==
- 域名: 留空（本机）
- 命令: 用户选择的exe文件路径
- 运行路径: 可自定义或使用exe文件所在目录

### 工作原理
使用lsrunase.exe工具以管理员权限执行选择的程序，避免了直接使用明文密码的安全风险。

## 注意事项

1. **安全性**: 本工具使用预设的管理员密码，请确保在安全的环境中使用
2. **兼容性**: 需要Windows系统支持，建议在Windows 7及以上版本使用
3. **权限**: 某些系统可能需要以管理员身份运行本工具

## 文件结构

```
一键提权/
├── Form1.cs                 # 主窗体代码
├── Form1.Designer.cs        # 窗体设计器代码
├── Form1.resx              # 窗体资源文件
├── Program.cs              # 程序入口点
├── 提权程序/
│   ├── lsrunase.exe        # 提权执行工具
│   └── 使用方法.txt        # lsrunase使用说明
└── README.md               # 本说明文件
```

## 开发环境

- .NET Framework 4.5.2
- Visual Studio 2015或更高版本
- Windows操作系统
- AntdUI 1.8.8 (通过NuGet自动安装)

## 许可证

本项目仅供学习和研究使用，请勿用于非法用途。
